# PDF to Audiobook Converter

A powerful Python application that converts PDF documents to high-quality audiobooks using ElevenLabs AI voice technology.

## Features

🎯 **Advanced PDF Processing**
- Multiple extraction methods (PDFPlumber, PyPDF2, PyMuPDF)
- Automatic chapter detection
- Text cleaning and optimization for TTS
- Support for complex PDF layouts

🎵 **High-Quality Audio Generation**
- ElevenLabs AI voices (5000+ voices, 31 languages)
- Automatic audio normalization and enhancement
- Chapter-based or continuous audiobook creation
- Multiple output formats (MP3, optimized streaming)

🖥️ **User-Friendly Interfaces**
- Simple GUI application
- Command-line interface
- Batch processing support
- Real-time conversion progress

📚 **Smart Features**
- Automatic chapter detection and separation
- AI-powered text enhancement for better TTS output
- Playlist generation (M3U format)
- Conversion reports and metadata
- Streaming optimization

## Installation

### 1. <PERSON>lone the Repository
```bash
git clone https://github.com/inkbytefo/pdf-to-audiobook.git
cd pdf-to-audiobook
```

### 2. Install Dependencies
```bash
python setup.py
```

### 3. Configure APIs
1. Copy `.env.example` to `.env`
2. Add your API keys:
```bash
# ElevenLabs API (Required)
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_VOICE_ID=your_preferred_voice_id

# OpenAI Compatible API (Optional - for text enhancement)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Text Enhancement Settings
ENABLE_TEXT_ENHANCEMENT=true
ENHANCEMENT_TYPE=comprehensive
```

## Usage

### GUI Application
```bash
python gui_interface.py
```

### Command Line Interface
```bash
# Basic conversion
python pdf_to_audiobook.py book.pdf

# Advanced options
python pdf_to_audiobook.py book.pdf -o ./audiobooks -v voice_id -e pdfplumber

# Enable AI text enhancement
python pdf_to_audiobook.py book.pdf --enhance-text --enhancement-type comprehensive

# Test AI connection
python pdf_to_audiobook.py --test-enhancement

# List available voices
python pdf_to_audiobook.py --list-voices

# Check usage information
python pdf_to_audiobook.py --usage-info
```

### Command Line Options
- `--output-dir, -o`: Output directory
- `--voice-id, -v`: ElevenLabs voice ID
- `--extraction-method, -e`: PDF extraction method (auto, pdfplumber, pypdf2, pymupdf)
- `--enhance-text`: Enable AI text enhancement
- `--enhancement-type`: Type of enhancement (comprehensive, spelling, grammar, tts_only)
- `--test-enhancement`: Test AI text enhancement connection
- `--list-voices`: List available voices
- `--usage-info`: Show API usage information
- `--verbose`: Enable verbose logging

## AI Text Enhancement

The application now supports AI-powered text enhancement to improve TTS output quality. This feature:

- **Fixes spelling and grammar errors** automatically
- **Optimizes text for TTS** by expanding abbreviations and converting numbers to words
- **Preserves original meaning** while improving readability
- **Supports multiple enhancement types** for different use cases

### Enhancement Types

1. **Comprehensive** (Default): Full text enhancement including spelling, grammar, and TTS optimization
2. **Spelling**: Only fixes spelling errors
3. **Grammar**: Fixes grammar and punctuation issues
4. **TTS Only**: Optimizes text specifically for text-to-speech (abbreviations, numbers, symbols)

### Supported APIs

The text enhancement feature works with any OpenAI-compatible API:
- OpenAI GPT models
- Azure OpenAI
- Local LLM servers (Ollama, LM Studio, etc.)
- Custom API endpoints

### Setup

1. Add your API credentials to `.env`:
```bash
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
ENABLE_TEXT_ENHANCEMENT=true
```

2. Test the connection:
```bash
python pdf_to_audiobook.py --test-enhancement
```

## Configuration

The application uses a configuration system that can be customized:

```python
config = {
    'elevenlabs': {
        'api_key': 'your_api_key',
        'voice_id': 'your_voice_id',
        'model': 'eleven_multilingual_v2'
    },
    'text_enhancement': {
        'enabled': True,
        'api_key': 'your_openai_api_key',
        'base_url': 'https://api.openai.com/v1',
        'model': 'gpt-3.5-turbo',
        'enhancement_type': 'comprehensive'
    },
    'audio': {
        'format': 'mp3',
        'bitrate': '128k',
        'sample_rate': 22050,
        'normalize': True,
        'add_pauses': True
    },
    'text': {
        'max_chunk_length': 5000,
        'clean_text': True,
        'detect_chapters': True
    },
    'output': {
        'create_combined': True,
        'create_chapters': True,
        'create_playlist': True,
        'optimize_streaming': False
    }
}
```

## Output Structure

```
output/
├── book_name/
│   ├── chapters/
│   │   ├── 01_Chapter_1.mp3
│   │   ├── 02_Chapter_2.mp3
│   │   └── ...
│   ├── complete_audiobook.mp3
│   ├── audiobook_streaming.mp3 (if enabled)
│   ├── audiobook_chapters.m3u
│   ├── audiobook_metadata.txt
│   └── conversion_report.json
```

## Supported PDF Types

- Text-based PDFs
- Scanned documents (with OCR capabilities)
- Academic papers and books
- Technical documentation
- Multi-column layouts
- Complex formatting

## ElevenLabs Voice Options

The application supports all ElevenLabs voices:
- **Multilingual voices**: Support for 31 languages
- **Voice categories**: Narration, conversational, characters
- **Custom voices**: Use your own cloned voices
- **Voice settings**: Adjustable stability, similarity, and style

## Performance Tips

1. **PDF Quality**: Higher quality PDFs produce better text extraction
2. **Chunk Size**: Adjust `max_chunk_length` for optimal processing
3. **Voice Selection**: Choose appropriate voices for content type
4. **Streaming**: Enable streaming optimization for mobile playback

## Troubleshooting

### Common Issues

**API Key Error**
```
Error: ElevenLabs API key not found
```
Solution: Set your API key in the `.env` file

**PDF Extraction Failed**
```
Error: All extraction methods failed
```
Solution: Try different extraction methods or check PDF quality

**Voice Not Found**
```
Error: Voice ID is required
```
Solution: Use `--list-voices` to see available voices

### Logs
Check the logs directory for detailed error information:
```
logs/pdf_to_audiobook.log
```

## API Usage and Costs

- **Free Tier**: 15 minutes/month
- **Paid Plans**: Starting from $5/month
- **Character Limits**: Varies by subscription
- **Optimization**: Use streaming mode to reduce file sizes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

**inkbytefo**
- GitHub: [@inkbytefo](https://github.com/inkbytefo)

## Acknowledgments

- [ElevenLabs](https://elevenlabs.io/) for amazing AI voice technology
- [PDFPlumber](https://github.com/jsvine/pdfplumber) for PDF processing
- [PyDub](https://github.com/jiaaro/pydub) for audio manipulation

## Support

If you encounter any issues or have questions:
1. Check the troubleshooting section
2. Review the logs
3. Open an issue on GitHub
4. Contact support

---

**Happy Audiobook Creation! 🎧📚**

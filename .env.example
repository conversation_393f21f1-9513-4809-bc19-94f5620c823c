# ElevenLabs API Configuration
ELEVENLABS_API_KEY=your_api_key_here

# Voice Settings
ELEVENLABS_VOICE_ID=your_preferred_voice_id

# OpenAI Compatible API Configuration (for text enhancement)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Text Enhancement Settings
ENABLE_TEXT_ENHANCEMENT=true
ENHANCEMENT_TYPE=comprehensive
# Options: comprehensive, spelling, grammar, tts_only

# Audio Settings
OUTPUT_FORMAT=mp3
AUDIO_QUALITY=high
CHUNK_SIZE=1000

# Processing Settings
MAX_CHUNK_LENGTH=5000
PAUSE_BETWEEN_CHAPTERS=2.0

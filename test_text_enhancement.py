#!/usr/bin/env python3
"""
Test script for AI text enhancement functionality
Author: inkbytefo
"""

import os
import sys
from dotenv import load_dotenv
from text_enhancer import TextEnhancer

def test_text_enhancement():
    """Test text enhancement functionality"""
    
    # Load environment variables
    load_dotenv()
    
    print("🧪 Testing AI Text Enhancement")
    print("=" * 50)
    
    # Initialize text enhancer
    enhancer = TextEnhancer()
    
    # Test connection
    print("\n1. Testing API connection...")
    connection_result = enhancer.test_connection()
    
    if connection_result['success']:
        print(f"✅ Connection successful!")
        print(f"   Model: {connection_result['model']}")
        print(f"   Base URL: {connection_result['base_url']}")
    else:
        print(f"❌ Connection failed: {connection_result['message']}")
        print("\n💡 Make sure to set these environment variables in .env:")
        print("   OPENAI_API_KEY=your_api_key_here")
        print("   OPENAI_BASE_URL=https://api.openai.com/v1")
        print("   OPENAI_MODEL=gpt-3.5-turbo")
        return
    
    # Test different enhancement types
    test_texts = [
        {
            "name": "Turkish text with errors",
            "text": "Bu bir test metnidir. Imla hatalari var ve 3 sayi bulunmaktadir. Dr. Ahmet'in kitabinda yazildigi gibi, % 50 oraninda basari elde edilmistir."
        },
        {
            "name": "Text with abbreviations",
            "text": "Prof. Dr. Mehmet Bey, vs. konularında uzman. Tel: 0532-123-4567, e-mail: <EMAIL> adresinden ulaşılabilir."
        },
        {
            "name": "Text with numbers and symbols",
            "text": "2023 yılında 1.500.000 TL gelir elde edildi. Bu %25'lik bir artış demek. & işareti ile @ sembolü de var."
        }
    ]
    
    enhancement_types = ["comprehensive", "spelling", "grammar", "tts_only"]
    
    for i, test_text in enumerate(test_texts, 1):
        print(f"\n{i}. Testing: {test_text['name']}")
        print("-" * 40)
        print(f"Original: {test_text['text']}")
        
        for enhancement_type in enhancement_types:
            print(f"\n   {enhancement_type.upper()} enhancement:")
            try:
                result = enhancer.enhance_text(test_text['text'], enhancement_type)
                print(f"   Enhanced: {result['enhanced']}")
                print(f"   Changes: {result['changes']}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    # Test chapter enhancement
    print(f"\n4. Testing chapter enhancement...")
    print("-" * 40)
    
    test_chapters = [
        {
            "title": "Bölüm 1",
            "content": "Bu birinci bölümdür. Imla hatalari var ve 5 sayi bulunmaktadir."
        },
        {
            "title": "Bölüm 2", 
            "content": "İkinci bölümde Dr. Ahmet'in teorileri anlatılmaktadır. %30 oranında başarı var."
        }
    ]
    
    try:
        enhanced_chapters = enhancer.enhance_chapters(test_chapters, "comprehensive")
        
        for i, chapter in enumerate(enhanced_chapters):
            print(f"\nChapter {i+1}: {chapter['title']}")
            print(f"Original: {chapter.get('original_content', 'N/A')}")
            print(f"Enhanced: {chapter['content']}")
            print(f"Changes: {chapter.get('enhancement_changes', 'N/A')}")
            
    except Exception as e:
        print(f"❌ Chapter enhancement error: {e}")
    
    print(f"\n✅ Text enhancement testing completed!")

if __name__ == "__main__":
    test_text_enhancement()

#!/usr/bin/env python3
"""
Example Usage Scripts for PDF to Audiobook Converter
Author: inkbytefo
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from pdf_to_audiobook import PDFToAudiobookConverter

def example_basic_conversion():
    """Example: Basic PDF to audiobook conversion"""
    print("📚 Example 1: Basic Conversion")
    print("-" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Initialize converter
    converter = PDFToAudiobookConverter()
    
    # Example PDF path (you need to provide a real PDF)
    pdf_path = "sample_book.pdf"
    
    if not Path(pdf_path).exists():
        print(f"⚠️  Sample PDF not found: {pdf_path}")
        print("Please place a PDF file named 'sample_book.pdf' in the project directory")
        return
    
    try:
        # Convert PDF to audiobook
        result = converter.convert_pdf_to_audiobook(
            pdf_path=pdf_path,
            output_dir="output/basic_example"
        )
        
        print("✅ Conversion completed!")
        print(f"📁 Output: {result['output_directory']}")
        print(f"📚 Chapters: {result['total_chapters']}")
        print(f"⏱️  Duration: {result['total_duration']/3600:.1f} hours")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_advanced_conversion():
    """Example: Advanced conversion with custom settings"""
    print("\n📚 Example 2: Advanced Conversion")
    print("-" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Custom configuration
    custom_config = {
        'elevenlabs': {
            'api_key': os.getenv('ELEVENLABS_API_KEY'),
            'voice_id': os.getenv('ELEVENLABS_VOICE_ID'),
            'model': 'eleven_multilingual_v2'
        },
        'audio': {
            'format': 'mp3',
            'bitrate': '192k',  # Higher quality
            'sample_rate': 44100,  # CD quality
            'normalize': True,
            'add_pauses': True
        },
        'text': {
            'max_chunk_length': 3000,  # Smaller chunks for better pacing
            'clean_text': True,
            'detect_chapters': True
        },
        'output': {
            'create_combined': True,
            'create_chapters': True,
            'create_playlist': True,
            'optimize_streaming': True  # Create streaming version
        }
    }
    
    # Initialize converter with custom config
    converter = PDFToAudiobookConverter(config=custom_config)
    
    pdf_path = "sample_book.pdf"
    
    if not Path(pdf_path).exists():
        print(f"⚠️  Sample PDF not found: {pdf_path}")
        return
    
    try:
        # Convert with specific voice and extraction method
        result = converter.convert_pdf_to_audiobook(
            pdf_path=pdf_path,
            output_dir="output/advanced_example",
            voice_id="specific_voice_id",  # Replace with actual voice ID
            extraction_method="pdfplumber"
        )
        
        print("✅ Advanced conversion completed!")
        print(f"📁 Output: {result['output_directory']}")
        print(f"🎵 Combined file: {result.get('combined_file', 'Not created')}")
        print(f"📱 Streaming file: {result.get('streaming_file', 'Not created')}")
        print(f"📋 Playlist: {result.get('playlist_file', 'Not created')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_batch_conversion():
    """Example: Batch convert multiple PDFs"""
    print("\n📚 Example 3: Batch Conversion")
    print("-" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Initialize converter
    converter = PDFToAudiobookConverter()
    
    # List of PDFs to convert
    pdf_files = [
        "book1.pdf",
        "book2.pdf", 
        "book3.pdf"
    ]
    
    # Filter existing files
    existing_files = [f for f in pdf_files if Path(f).exists()]
    
    if not existing_files:
        print("⚠️  No PDF files found for batch conversion")
        print("Please place PDF files in the project directory")
        return
    
    print(f"📚 Found {len(existing_files)} PDF files to convert")
    
    results = []
    
    for i, pdf_file in enumerate(existing_files, 1):
        print(f"\n🔄 Converting {i}/{len(existing_files)}: {pdf_file}")
        
        try:
            result = converter.convert_pdf_to_audiobook(
                pdf_path=pdf_file,
                output_dir=f"output/batch/{Path(pdf_file).stem}"
            )
            
            results.append({
                'file': pdf_file,
                'status': 'success',
                'result': result
            })
            
            print(f"✅ {pdf_file} converted successfully")
            
        except Exception as e:
            results.append({
                'file': pdf_file,
                'status': 'failed',
                'error': str(e)
            })
            
            print(f"❌ {pdf_file} failed: {e}")
    
    # Summary
    print(f"\n📊 Batch Conversion Summary")
    print("-" * 30)
    successful = len([r for r in results if r['status'] == 'success'])
    failed = len([r for r in results if r['status'] == 'failed'])
    
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Output directory: output/batch/")

def example_voice_selection():
    """Example: List and select voices"""
    print("\n🎤 Example 4: Voice Selection")
    print("-" * 40)
    
    # Load environment variables
    load_dotenv()
    
    try:
        # Initialize converter
        converter = PDFToAudiobookConverter()
        
        # Get available voices
        voices = converter.list_available_voices()
        
        print(f"🎵 Available voices: {len(voices)}")
        print("\nTop 10 voices:")
        
        for i, voice in enumerate(voices[:10], 1):
            print(f"{i:2d}. {voice['voice_id'][:20]:<20} - {voice['name']:<25} ({voice['category']})")
        
        # Show usage information
        usage_info = converter.get_usage_info()
        print(f"\n📊 API Usage:")
        print(f"Character limit: {usage_info['character_limit']:,}")
        print(f"Characters used: {usage_info['character_count']:,}")
        remaining = usage_info['character_limit'] - usage_info['character_count']
        print(f"Characters remaining: {remaining:,}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_text_processing():
    """Example: Text extraction and processing"""
    print("\n📝 Example 5: Text Processing")
    print("-" * 40)
    
    from pdf_extractor import PDFExtractor, TextCleaner
    
    pdf_path = "sample_book.pdf"
    
    if not Path(pdf_path).exists():
        print(f"⚠️  Sample PDF not found: {pdf_path}")
        return
    
    try:
        # Initialize extractor
        extractor = PDFExtractor()
        text_cleaner = TextCleaner()
        
        # Extract text
        print("🔍 Extracting text from PDF...")
        result = extractor.extract_text(pdf_path, method="auto")
        
        print(f"📄 Pages: {result['metadata']['pages']}")
        print(f"📚 Chapters detected: {len(result['chapters'])}")
        print(f"📝 Total characters: {len(result['text']):,}")
        
        # Show chapter information
        print("\n📖 Chapter Information:")
        for i, chapter in enumerate(result['chapters'][:5], 1):  # Show first 5 chapters
            print(f"{i}. {chapter['title'][:50]}...")
            print(f"   Content length: {len(chapter['content']):,} characters")
        
        # Clean text
        print("\n🧹 Cleaning text...")
        cleaned_text = text_cleaner.clean_text(result['text'])
        
        # Split into chunks
        chunks = text_cleaner.split_into_chunks(cleaned_text, max_length=5000)
        print(f"📦 Text chunks: {len(chunks)}")
        
        # Estimate conversion time and cost
        total_chars = len(cleaned_text)
        estimated_minutes = total_chars / 1000 * 0.5  # Rough estimate
        
        print(f"\n📊 Conversion Estimates:")
        print(f"Characters to convert: {total_chars:,}")
        print(f"Estimated audio length: {estimated_minutes:.1f} minutes")
        print(f"Estimated cost: ${estimated_minutes * 0.08:.2f} (Business plan)")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all examples"""
    print("🎯 PDF to Audiobook Converter - Usage Examples")
    print("=" * 60)
    
    # Check if API key is set
    load_dotenv()
    api_key = os.getenv('ELEVENLABS_API_KEY')
    
    if not api_key:
        print("⚠️  Warning: ELEVENLABS_API_KEY not found in environment")
        print("Please set your API key in the .env file to run conversion examples")
        print("\nRunning text processing example only...\n")
        example_text_processing()
        return
    
    # Run examples
    try:
        example_voice_selection()
        example_text_processing()
        example_basic_conversion()
        example_advanced_conversion()
        example_batch_conversion()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    print("\n✅ Examples completed!")
    print("\n💡 Tips:")
    print("- Place PDF files in the project directory to test conversions")
    print("- Check the output/ directory for generated audiobooks")
    print("- Use the GUI interface for easier conversions: python gui_interface.py")

if __name__ == "__main__":
    main()

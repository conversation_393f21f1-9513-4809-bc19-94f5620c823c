#!/usr/bin/env python3
"""
ElevenLabs TTS Integration Module
Author: inkbytefo
"""

import os
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional, Union
import requests
from tqdm import tqdm

try:
    from elevenlabs import ElevenLabs, Voice, VoiceSettings
except ImportError:
    try:
        from elevenlabs.client import ElevenLabs
        from elevenlabs import Voice, VoiceSettings
    except ImportError:
        # Fallback for older versions
        from elevenlabs import generate, voices, set_api_key
        ElevenLabs = None

class ElevenLabsTTS:
    """ElevenLabs Text-to-Speech integration with advanced features"""
    
    def __init__(self, api_key: str = None, voice_id: str = None):
        """
        Initialize ElevenLabs TTS client

        Args:
            api_key: ElevenLabs API key
            voice_id: Default voice ID to use
        """
        self.api_key = api_key or os.getenv('ELEVENLABS_API_KEY')
        self.voice_id = voice_id or os.getenv('ELEVENLABS_VOICE_ID')

        if not self.api_key:
            raise ValueError("ElevenLabs API key is required")

        # Initialize client based on available version
        if Eleven<PERSON>abs is not None:
            self.client = ElevenLabs(api_key=self.api_key)
        else:
            # Fallback for older versions
            set_api_key(self.api_key)
            self.client = None

        self.logger = logging.getLogger(__name__)

        # Default settings
        try:
            self.voice_settings = VoiceSettings(
                stability=0.5,
                similarity_boost=0.8,
                style=0.2,
                use_speaker_boost=True
            )
        except:
            # Fallback for older versions
            self.voice_settings = {
                'stability': 0.5,
                'similarity_boost': 0.8,
                'style': 0.2,
                'use_speaker_boost': True
            }

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get list of available voices"""
        try:
            voices = self.client.voices.get_all()
            voice_list = []
            
            for voice in voices.voices:
                voice_list.append({
                    "voice_id": voice.voice_id,
                    "name": voice.name,
                    "category": voice.category,
                    "description": getattr(voice, 'description', ''),
                    "preview_url": getattr(voice, 'preview_url', '')
                })
            
            self.logger.info(f"Found {len(voice_list)} available voices")
            return voice_list
            
        except Exception as e:
            self.logger.error(f"Error fetching voices: {e}")
            return []
    
    def set_voice(self, voice_id: str, voice_settings: Dict = None):
        """Set the voice and settings to use"""
        self.voice_id = voice_id
        
        if voice_settings:
            self.voice_settings = VoiceSettings(
                stability=voice_settings.get('stability', 0.5),
                similarity_boost=voice_settings.get('similarity_boost', 0.8),
                style=voice_settings.get('style', 0.2),
                use_speaker_boost=voice_settings.get('use_speaker_boost', True)
            )
        
        self.logger.info(f"Voice set to: {voice_id}")
    
    def text_to_speech(self, text: str, output_path: str = None, 
                      voice_id: str = None, model: str = "eleven_multilingual_v2") -> bytes:
        """
        Convert text to speech
        
        Args:
            text: Text to convert
            output_path: Optional path to save audio file
            voice_id: Voice ID to use (overrides default)
            model: TTS model to use
            
        Returns:
            Audio data as bytes
        """
        if not text.strip():
            raise ValueError("Text cannot be empty")
        
        voice_id = voice_id or self.voice_id
        if not voice_id:
            raise ValueError("Voice ID is required")
        
        # Rate limiting
        self._wait_for_rate_limit()
        
        try:
            self.logger.info(f"Converting text to speech: {len(text)} characters")
            
            # Generate audio
            audio_generator = self.client.generate(
                text=text,
                voice=Voice(
                    voice_id=voice_id,
                    settings=self.voice_settings
                ),
                model=model
            )
            
            # Collect audio data
            audio_data = b"".join(audio_generator)
            
            # Save to file if path provided
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'wb') as f:
                    f.write(audio_data)
                
                self.logger.info(f"Audio saved to: {output_path}")
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"TTS conversion failed: {e}")
            raise
    
    def convert_chunks(self, text_chunks: List[str], output_dir: str, 
                      base_filename: str = "chunk", voice_id: str = None,
                      model: str = "eleven_multilingual_v2") -> List[str]:
        """
        Convert multiple text chunks to audio files
        
        Args:
            text_chunks: List of text chunks
            output_dir: Directory to save audio files
            base_filename: Base filename for audio files
            voice_id: Voice ID to use
            model: TTS model to use
            
        Returns:
            List of generated audio file paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        audio_files = []
        
        self.logger.info(f"Converting {len(text_chunks)} chunks to audio")
        
        for i, chunk in enumerate(tqdm(text_chunks, desc="Converting to audio")):
            if not chunk.strip():
                continue
                
            try:
                # Generate filename
                filename = f"{base_filename}_{i+1:03d}.mp3"
                output_path = output_dir / filename
                
                # Convert chunk
                self.text_to_speech(
                    text=chunk,
                    output_path=output_path,
                    voice_id=voice_id,
                    model=model
                )
                
                audio_files.append(str(output_path))
                
            except Exception as e:
                self.logger.error(f"Failed to convert chunk {i+1}: {e}")
                continue
        
        self.logger.info(f"Successfully converted {len(audio_files)} chunks")
        return audio_files
    
    def convert_chapters(self, chapters: List[Dict], output_dir: str,
                        voice_id: str = None, model: str = "eleven_multilingual_v2") -> List[Dict]:
        """
        Convert book chapters to audio files
        
        Args:
            chapters: List of chapter dictionaries with 'title' and 'content'
            output_dir: Directory to save audio files
            voice_id: Voice ID to use
            model: TTS model to use
            
        Returns:
            List of chapter info with audio file paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        converted_chapters = []
        
        self.logger.info(f"Converting {len(chapters)} chapters to audio")
        
        for i, chapter in enumerate(tqdm(chapters, desc="Converting chapters")):
            try:
                # Clean chapter title for filename
                title = chapter.get('title', f'Chapter {i+1}')
                safe_title = self._sanitize_filename(title)
                filename = f"{i+1:02d}_{safe_title}.mp3"
                output_path = output_dir / filename
                
                # Convert chapter content
                content = chapter.get('content', '')
                if not content.strip():
                    self.logger.warning(f"Chapter {i+1} has no content, skipping")
                    continue
                
                self.text_to_speech(
                    text=content,
                    output_path=output_path,
                    voice_id=voice_id,
                    model=model
                )
                
                converted_chapters.append({
                    'title': title,
                    'content': content,
                    'audio_file': str(output_path),
                    'chapter_number': i + 1
                })
                
            except Exception as e:
                self.logger.error(f"Failed to convert chapter {i+1}: {e}")
                continue
        
        self.logger.info(f"Successfully converted {len(converted_chapters)} chapters")
        return converted_chapters
    
    def _wait_for_rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for cross-platform compatibility"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        filename = filename[:50]
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        return filename or "untitled"
    
    def get_character_limit(self) -> int:
        """Get character limit for current subscription"""
        try:
            user_info = self.client.user.get()
            return getattr(user_info, 'character_limit', 10000)
        except:
            return 10000  # Default fallback
    
    def get_character_count(self) -> int:
        """Get current character usage"""
        try:
            user_info = self.client.user.get()
            return getattr(user_info, 'character_count', 0)
        except:
            return 0  # Default fallback

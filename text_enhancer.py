#!/usr/bin/env python3
"""
AI-Powered Text Enhancement Module
Author: inkbytefo
"""

import os
import logging
import time
from typing import Dict, List, Optional, Union
import requests
import json
from pathlib import Path

class TextEnhancer:
    """AI-powered text enhancement for TTS optimization using OpenAI compatible APIs"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model: str = None):
        """
        Initialize Text Enhancer
        
        Args:
            api_key: API key for the service
            base_url: Base URL for OpenAI compatible API
            model: Model name to use
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.base_url = base_url or os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
        self.model = model or os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        
        self.logger = logging.getLogger(__name__)
        
        # Ensure base_url ends with /v1 if it's OpenAI format
        if not self.base_url.endswith('/v1') and 'openai.com' in self.base_url:
            self.base_url = self.base_url.rstrip('/') + '/v1'
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests
        
        # Enhancement settings
        self.enhancement_settings = {
            'fix_spelling': True,
            'fix_grammar': True,
            'improve_punctuation': True,
            'optimize_for_tts': True,
            'preserve_meaning': True,
            'maintain_style': True
        }
    
    def enhance_text(self, text: str, enhancement_type: str = "comprehensive") -> Dict[str, str]:
        """
        Enhance text using AI for better TTS output
        
        Args:
            text: Text to enhance
            enhancement_type: Type of enhancement (comprehensive, spelling, grammar, tts_only)
            
        Returns:
            Dictionary with original and enhanced text
        """
        if not text.strip():
            return {"original": text, "enhanced": text, "changes": "No text provided"}
        
        if not self.api_key:
            self.logger.warning("No API key provided, returning original text")
            return {"original": text, "enhanced": text, "changes": "No API key"}
        
        try:
            self.logger.info(f"Enhancing text: {len(text)} characters")
            
            # Get appropriate prompt based on enhancement type
            system_prompt = self._get_enhancement_prompt(enhancement_type)
            
            # Rate limiting
            self._wait_for_rate_limit()
            
            # Make API request
            enhanced_text = self._make_api_request(text, system_prompt)
            
            # Validate enhancement
            if self._validate_enhancement(text, enhanced_text):
                self.logger.info("Text enhancement completed successfully")
                changes = self._detect_changes(text, enhanced_text)
                return {
                    "original": text,
                    "enhanced": enhanced_text,
                    "changes": changes
                }
            else:
                self.logger.warning("Enhancement validation failed, returning original text")
                return {
                    "original": text,
                    "enhanced": text,
                    "changes": "Enhancement validation failed"
                }
                
        except Exception as e:
            self.logger.error(f"Text enhancement failed: {e}")
            return {
                "original": text,
                "enhanced": text,
                "changes": f"Enhancement failed: {str(e)}"
            }
    
    def enhance_chapters(self, chapters: List[Dict], enhancement_type: str = "comprehensive") -> List[Dict]:
        """
        Enhance multiple chapters
        
        Args:
            chapters: List of chapter dictionaries
            enhancement_type: Type of enhancement
            
        Returns:
            List of enhanced chapters
        """
        enhanced_chapters = []
        
        self.logger.info(f"Enhancing {len(chapters)} chapters")
        
        for i, chapter in enumerate(chapters, 1):
            try:
                self.logger.info(f"Enhancing chapter {i}/{len(chapters)}: {chapter.get('title', 'Untitled')}")
                
                content = chapter.get('content', '')
                if not content.strip():
                    enhanced_chapters.append(chapter)
                    continue
                
                # Enhance chapter content
                enhancement_result = self.enhance_text(content, enhancement_type)
                
                # Create enhanced chapter
                enhanced_chapter = chapter.copy()
                enhanced_chapter['content'] = enhancement_result['enhanced']
                enhanced_chapter['original_content'] = enhancement_result['original']
                enhanced_chapter['enhancement_changes'] = enhancement_result['changes']
                
                enhanced_chapters.append(enhanced_chapter)
                
            except Exception as e:
                self.logger.error(f"Failed to enhance chapter {i}: {e}")
                enhanced_chapters.append(chapter)  # Keep original on error
        
        self.logger.info(f"Enhanced {len(enhanced_chapters)} chapters")
        return enhanced_chapters
    
    def _get_enhancement_prompt(self, enhancement_type: str) -> str:
        """Get system prompt based on enhancement type"""
        
        base_instructions = """Sen bir metin düzenleme uzmanısın. Verilen metni aşağıdaki kurallara göre düzenle:

TEMEL KURALLAR:
1. Metnin anlamını ve içeriğini kesinlikle değiştirme
2. Yazarın üslubunu ve tonunu koru
3. Sadece teknik düzeltmeler yap

"""
        
        if enhancement_type == "comprehensive":
            return base_instructions + """
YAPILACAK DÜZELTMELER:
- İmla hatalarını düzelt
- Dilbilgisi hatalarını düzelt
- Noktalama işaretlerini düzelt
- TTS için uygun olmayan karakterleri düzelt
- Kısaltmaları açık hale getir (Dr. → Doktor)
- Sayıları yazıyla ifade et (3 → üç)
- Sesli okuma için uygun olmayan sembolleri düzelt

ÇIKTI FORMATI:
Sadece düzeltilmiş metni döndür, başka açıklama yapma.
"""
        
        elif enhancement_type == "spelling":
            return base_instructions + """
YAPILACAK DÜZELTMELER:
- Sadece imla hatalarını düzelt
- Kelime yazımlarını kontrol et
- Türkçe karakter kullanımını düzelt

ÇIKTI FORMATI:
Sadece düzeltilmiş metni döndür.
"""
        
        elif enhancement_type == "grammar":
            return base_instructions + """
YAPILACAK DÜZELTMELER:
- Dilbilgisi hatalarını düzelt
- Cümle yapılarını düzelt
- Noktalama işaretlerini düzelt

ÇIKTI FORMATI:
Sadece düzeltilmiş metni döndür.
"""
        
        elif enhancement_type == "tts_only":
            return base_instructions + """
YAPILACAK DÜZELTMELER:
- TTS için uygun olmayan karakterleri düzelt
- Kısaltmaları açık hale getir (Dr. → Doktor, vs. → versus)
- Sayıları yazıyla ifade et (3 → üç, 2023 → iki bin yirmi üç)
- Özel karakterleri düzelt (& → ve, % → yüzde)
- URL'leri kaldır veya açıkla

ÇIKTI FORMATI:
Sadece düzeltilmiş metni döndür.
"""
        
        else:
            return base_instructions + "Metni genel olarak düzelt ve sadece düzeltilmiş metni döndür."
    
    def _make_api_request(self, text: str, system_prompt: str) -> str:
        """Make API request to OpenAI compatible endpoint"""
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # Split text if too long
        max_chunk_size = 3000  # Conservative limit for API
        if len(text) > max_chunk_size:
            return self._enhance_long_text(text, system_prompt, max_chunk_size)
        
        data = {
            'model': self.model,
            'messages': [
                {'role': 'system', 'content': system_prompt},
                {'role': 'user', 'content': text}
            ],
            'temperature': 0.1,  # Low temperature for consistent corrections
            'max_tokens': len(text) + 500  # Allow some expansion
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            enhanced_text = result['choices'][0]['message']['content'].strip()
            return enhanced_text
        else:
            raise Exception(f"API request failed: {response.status_code} - {response.text}")
    
    def _enhance_long_text(self, text: str, system_prompt: str, chunk_size: int) -> str:
        """Enhance long text by splitting into chunks"""
        
        # Split text into sentences to avoid breaking mid-sentence
        sentences = text.split('. ')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Enhance each chunk
        enhanced_chunks = []
        for i, chunk in enumerate(chunks):
            self.logger.info(f"Enhancing chunk {i+1}/{len(chunks)}")
            enhanced_chunk = self._make_api_request(chunk, system_prompt)
            enhanced_chunks.append(enhanced_chunk)
            
            # Rate limiting between chunks
            if i < len(chunks) - 1:
                time.sleep(self.min_request_interval)
        
        return " ".join(enhanced_chunks)
    
    def _validate_enhancement(self, original: str, enhanced: str) -> bool:
        """Validate that enhancement is reasonable"""
        
        # Basic validation checks
        if not enhanced or not enhanced.strip():
            return False
        
        # Check if text length changed dramatically (more than 50% change is suspicious)
        length_ratio = len(enhanced) / len(original) if len(original) > 0 else 0
        if length_ratio < 0.5 or length_ratio > 1.5:
            self.logger.warning(f"Text length changed dramatically: {length_ratio:.2f}x")
            return False
        
        # Check if enhancement is just the original text
        if enhanced.strip() == original.strip():
            return True  # No changes needed is valid
        
        return True
    
    def _detect_changes(self, original: str, enhanced: str) -> str:
        """Detect and summarize changes made"""
        
        if original.strip() == enhanced.strip():
            return "No changes made"
        
        # Simple change detection
        changes = []
        
        if len(enhanced) != len(original):
            length_diff = len(enhanced) - len(original)
            changes.append(f"Length changed by {length_diff} characters")
        
        # Count basic differences
        original_words = set(original.lower().split())
        enhanced_words = set(enhanced.lower().split())
        
        added_words = enhanced_words - original_words
        removed_words = original_words - enhanced_words
        
        if added_words:
            changes.append(f"Added {len(added_words)} new words")
        if removed_words:
            changes.append(f"Removed {len(removed_words)} words")
        
        return "; ".join(changes) if changes else "Text enhanced"
    
    def _wait_for_rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def test_connection(self) -> Dict[str, Union[bool, str]]:
        """Test API connection"""
        try:
            test_text = "Bu bir test metnidir."
            result = self.enhance_text(test_text, "spelling")
            
            return {
                "success": True,
                "message": "API connection successful",
                "model": self.model,
                "base_url": self.base_url
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"API connection failed: {str(e)}",
                "model": self.model,
                "base_url": self.base_url
            }

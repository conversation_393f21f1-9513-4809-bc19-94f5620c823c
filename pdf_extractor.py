#!/usr/bin/env python3
"""
PDF Text Extraction Module
Author: inkbytefo
"""

import re
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import PyPDF2
import pdfplumber
import fitz  # pymupdf

class PDFExtractor:
    """Advanced PDF text extraction with multiple fallback methods"""
    
    def __init__(self, log_level=logging.INFO):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)
        
    def extract_text(self, pdf_path: str, method: str = "auto") -> Dict[str, any]:
        """
        Extract text from PDF using specified method
        
        Args:
            pdf_path: Path to PDF file
            method: Extraction method ('auto', 'pdfplumber', 'pypdf2', 'pymupdf')
            
        Returns:
            Dict with extracted text, metadata, and chapter info
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
            
        self.logger.info(f"Extracting text from: {pdf_path.name}")
        
        if method == "auto":
            return self._extract_auto(pdf_path)
        elif method == "pdfplumber":
            return self._extract_pdfplumber(pdf_path)
        elif method == "pypdf2":
            return self._extract_pypdf2(pdf_path)
        elif method == "pymupdf":
            return self._extract_pymupdf(pdf_path)
        else:
            raise ValueError(f"Unknown extraction method: {method}")
    
    def _extract_auto(self, pdf_path: Path) -> Dict[str, any]:
        """Try multiple extraction methods and return the best result"""
        methods = ["pdfplumber", "pymupdf", "pypdf2"]
        best_result = None
        best_score = 0
        
        for method in methods:
            try:
                result = getattr(self, f"_extract_{method}")(pdf_path)
                score = self._evaluate_extraction_quality(result["text"])
                
                self.logger.info(f"Method {method}: {score:.2f} quality score")
                
                if score > best_score:
                    best_score = score
                    best_result = result
                    best_result["extraction_method"] = method
                    
            except Exception as e:
                self.logger.warning(f"Method {method} failed: {e}")
                continue
        
        if best_result is None:
            raise Exception("All extraction methods failed")
            
        self.logger.info(f"Best method: {best_result['extraction_method']} (score: {best_score:.2f})")
        return best_result
    
    def _extract_pdfplumber(self, pdf_path: Path) -> Dict[str, any]:
        """Extract using pdfplumber (best for complex layouts)"""
        text_content = []
        metadata = {}
        
        with pdfplumber.open(pdf_path) as pdf:
            metadata = {
                "title": pdf.metadata.get("Title", ""),
                "author": pdf.metadata.get("Author", ""),
                "pages": len(pdf.pages)
            }
            
            for page_num, page in enumerate(pdf.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append({
                            "page": page_num,
                            "text": page_text.strip()
                        })
                except Exception as e:
                    self.logger.warning(f"Error extracting page {page_num}: {e}")
        
        full_text = "\n\n".join([page["text"] for page in text_content])
        
        return {
            "text": full_text,
            "pages": text_content,
            "metadata": metadata,
            "chapters": self._detect_chapters(full_text)
        }
    
    def _extract_pypdf2(self, pdf_path: Path) -> Dict[str, any]:
        """Extract using PyPDF2 (fast but basic)"""
        text_content = []
        metadata = {}
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            metadata = {
                "title": pdf_reader.metadata.get("/Title", "") if pdf_reader.metadata else "",
                "author": pdf_reader.metadata.get("/Author", "") if pdf_reader.metadata else "",
                "pages": len(pdf_reader.pages)
            }
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append({
                            "page": page_num,
                            "text": page_text.strip()
                        })
                except Exception as e:
                    self.logger.warning(f"Error extracting page {page_num}: {e}")
        
        full_text = "\n\n".join([page["text"] for page in text_content])
        
        return {
            "text": full_text,
            "pages": text_content,
            "metadata": metadata,
            "chapters": self._detect_chapters(full_text)
        }
    
    def _extract_pymupdf(self, pdf_path: Path) -> Dict[str, any]:
        """Extract using PyMuPDF (good balance of speed and quality)"""
        text_content = []
        metadata = {}
        
        doc = fitz.open(pdf_path)
        
        metadata = {
            "title": doc.metadata.get("title", ""),
            "author": doc.metadata.get("author", ""),
            "pages": doc.page_count
        }
        
        for page_num in range(doc.page_count):
            try:
                page = doc[page_num]
                page_text = page.get_text()
                if page_text:
                    text_content.append({
                        "page": page_num + 1,
                        "text": page_text.strip()
                    })
            except Exception as e:
                self.logger.warning(f"Error extracting page {page_num + 1}: {e}")
        
        doc.close()
        
        full_text = "\n\n".join([page["text"] for page in text_content])
        
        return {
            "text": full_text,
            "pages": text_content,
            "metadata": metadata,
            "chapters": self._detect_chapters(full_text)
        }
    
    def _evaluate_extraction_quality(self, text: str) -> float:
        """Evaluate the quality of extracted text"""
        if not text:
            return 0.0
        
        # Basic quality metrics
        word_count = len(text.split())
        char_count = len(text)
        
        # Check for common extraction artifacts
        artifacts = [
            r'\s{3,}',  # Multiple spaces
            r'[^\w\s]{5,}',  # Long sequences of special chars
            r'\n{3,}',  # Multiple newlines
        ]
        
        artifact_penalty = 0
        for pattern in artifacts:
            matches = len(re.findall(pattern, text))
            artifact_penalty += matches * 0.1
        
        # Calculate score
        base_score = min(word_count / 100, 10.0)  # Max 10 points for word count
        quality_score = max(0, base_score - artifact_penalty)
        
        return quality_score
    
    def _detect_chapters(self, text: str) -> List[Dict[str, any]]:
        """Detect chapter boundaries in text"""
        chapters = []
        
        # Common chapter patterns
        patterns = [
            r'^(Chapter\s+\d+|CHAPTER\s+\d+|Bölüm\s+\d+|BÖLÜM\s+\d+)',
            r'^(\d+\.\s+[A-Z][^.]{10,50})',
            r'^([A-Z][A-Z\s]{5,50})\n',
        ]
        
        lines = text.split('\n')
        current_chapter = None
        chapter_content = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Check if this line is a chapter header
            is_chapter = False
            for pattern in patterns:
                if re.match(pattern, line, re.MULTILINE):
                    is_chapter = True
                    break
            
            if is_chapter:
                # Save previous chapter
                if current_chapter and chapter_content:
                    chapters.append({
                        "title": current_chapter,
                        "content": "\n".join(chapter_content).strip(),
                        "start_line": chapters[-1]["end_line"] + 1 if chapters else 0,
                        "end_line": i
                    })
                
                # Start new chapter
                current_chapter = line
                chapter_content = []
            else:
                chapter_content.append(line)
        
        # Add final chapter
        if current_chapter and chapter_content:
            chapters.append({
                "title": current_chapter,
                "content": "\n".join(chapter_content).strip(),
                "start_line": chapters[-1]["end_line"] + 1 if chapters else 0,
                "end_line": len(lines)
            })
        
        # If no chapters detected, treat entire text as one chapter
        if not chapters:
            chapters.append({
                "title": "Full Document",
                "content": text.strip(),
                "start_line": 0,
                "end_line": len(lines)
            })
        
        self.logger.info(f"Detected {len(chapters)} chapters")
        return chapters

class TextCleaner:
    """Clean and preprocess extracted text for TTS"""

    def __init__(self):
        self.unwanted_patterns = [
            r'\f',  # Form feed
            r'\x0c',  # Page break
            r'Page \d+',  # Page numbers
            r'^\d+$',  # Standalone numbers
            r'www\.[^\s]+',  # URLs
            r'http[s]?://[^\s]+',  # URLs
            r'\b[A-Z]{2,}\b(?:\s+[A-Z]{2,}\b)*',  # ALL CAPS sequences
        ]

    def clean_text(self, text: str) -> str:
        """Clean text for better TTS output"""
        if not text:
            return ""

        # Remove unwanted patterns
        for pattern in self.unwanted_patterns:
            text = re.sub(pattern, '', text, flags=re.MULTILINE)

        # Fix common issues
        text = self._fix_spacing(text)
        text = self._fix_punctuation(text)
        text = self._fix_abbreviations(text)

        return text.strip()

    def _fix_spacing(self, text: str) -> str:
        """Fix spacing issues"""
        # Multiple spaces to single space
        text = re.sub(r'\s+', ' ', text)

        # Multiple newlines to double newline
        text = re.sub(r'\n{3,}', '\n\n', text)

        # Remove spaces before punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)

        return text

    def _fix_punctuation(self, text: str) -> str:
        """Fix punctuation for better TTS"""
        # Add periods to sentences without ending punctuation
        sentences = text.split('\n')
        fixed_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and not sentence[-1] in '.!?':
                if len(sentence) > 10:  # Only for substantial sentences
                    sentence += '.'
            fixed_sentences.append(sentence)

        return '\n'.join(fixed_sentences)

    def _fix_abbreviations(self, text: str) -> str:
        """Expand common abbreviations for better pronunciation"""
        abbreviations = {
            r'\bDr\.': 'Doctor',
            r'\bMr\.': 'Mister',
            r'\bMrs\.': 'Missus',
            r'\bMs\.': 'Miss',
            r'\bProf\.': 'Professor',
            r'\betc\.': 'etcetera',
            r'\bi\.e\.': 'that is',
            r'\be\.g\.': 'for example',
            r'\bvs\.': 'versus',
        }

        for abbrev, expansion in abbreviations.items():
            text = re.sub(abbrev, expansion, text, flags=re.IGNORECASE)

        return text

    def split_into_chunks(self, text: str, max_length: int = 5000) -> List[str]:
        """Split text into chunks suitable for TTS processing"""
        if len(text) <= max_length:
            return [text]

        chunks = []
        sentences = re.split(r'[.!?]+', text)
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Add sentence ending if missing
            if not sentence.endswith(('.', '!', '?')):
                sentence += '.'

            # Check if adding this sentence would exceed limit
            if len(current_chunk) + len(sentence) + 1 > max_length:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # Sentence is too long, split it further
                    words = sentence.split()
                    temp_chunk = ""
                    for word in words:
                        if len(temp_chunk) + len(word) + 1 > max_length:
                            if temp_chunk:
                                chunks.append(temp_chunk.strip())
                                temp_chunk = word
                            else:
                                chunks.append(word)  # Single word too long
                        else:
                            temp_chunk += " " + word if temp_chunk else word
                    current_chunk = temp_chunk
            else:
                current_chunk += " " + sentence if current_chunk else sentence

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

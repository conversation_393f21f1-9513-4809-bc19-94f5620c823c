#!/usr/bin/env python3
"""
Audio Processing and Optimization Module
Author: inkbytefo
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from pydub import AudioSegment
from pydub.effects import normalize, compress_dynamic_range
from tqdm import tqdm

class AudioProcessor:
    """Advanced audio processing for audiobook creation"""
    
    def __init__(self, log_level=logging.INFO):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)
        
        # Default settings
        self.default_format = "mp3"
        self.default_bitrate = "128k"
        self.default_sample_rate = 22050
        self.chapter_pause_duration = 2000  # 2 seconds in milliseconds
        self.sentence_pause_duration = 500   # 0.5 seconds
        
    def combine_audio_files(self, audio_files: List[str], output_path: str,
                           add_pauses: bool = True, normalize_audio: bool = True,
                           fade_in_out: bool = True) -> str:
        """
        Combine multiple audio files into a single audiobook
        
        Args:
            audio_files: List of audio file paths
            output_path: Output file path
            add_pauses: Add pauses between segments
            normalize_audio: Normalize audio levels
            fade_in_out: Add fade in/out effects
            
        Returns:
            Path to combined audio file
        """
        if not audio_files:
            raise ValueError("No audio files provided")
        
        self.logger.info(f"Combining {len(audio_files)} audio files")
        
        combined_audio = AudioSegment.empty()
        
        for i, audio_file in enumerate(tqdm(audio_files, desc="Combining audio")):
            try:
                # Load audio segment
                audio_segment = AudioSegment.from_file(audio_file)
                
                # Normalize if requested
                if normalize_audio:
                    audio_segment = normalize(audio_segment)
                
                # Add fade effects
                if fade_in_out:
                    audio_segment = audio_segment.fade_in(100).fade_out(100)
                
                # Add to combined audio
                combined_audio += audio_segment
                
                # Add pause between segments (except for last file)
                if add_pauses and i < len(audio_files) - 1:
                    pause = AudioSegment.silent(duration=self.chapter_pause_duration)
                    combined_audio += pause
                
            except Exception as e:
                self.logger.error(f"Error processing {audio_file}: {e}")
                continue
        
        # Export combined audio
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        export_params = {
            "format": self.default_format,
            "bitrate": self.default_bitrate,
            "parameters": ["-ar", str(self.default_sample_rate)]
        }
        
        combined_audio.export(output_path, **export_params)
        
        self.logger.info(f"Combined audiobook saved to: {output_path}")
        self.logger.info(f"Total duration: {len(combined_audio) / 1000:.1f} seconds")
        
        return str(output_path)
    
    def create_chapter_audiobook(self, chapters: List[Dict], output_dir: str,
                               create_combined: bool = True) -> Dict[str, any]:
        """
        Create audiobook with separate chapter files and optional combined file
        
        Args:
            chapters: List of chapter dictionaries with audio_file paths
            output_dir: Output directory
            create_combined: Whether to create a combined audiobook file
            
        Returns:
            Dictionary with audiobook information
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        processed_chapters = []
        chapter_files = []
        
        self.logger.info(f"Processing {len(chapters)} chapters")
        
        # Process individual chapters
        for chapter in tqdm(chapters, desc="Processing chapters"):
            try:
                audio_file = chapter.get('audio_file')
                if not audio_file or not Path(audio_file).exists():
                    self.logger.warning(f"Audio file not found: {audio_file}")
                    continue
                
                # Load and process audio
                audio = AudioSegment.from_file(audio_file)
                
                # Normalize and enhance
                audio = normalize(audio)
                audio = compress_dynamic_range(audio)
                
                # Create output filename
                chapter_num = chapter.get('chapter_number', len(processed_chapters) + 1)
                title = chapter.get('title', f'Chapter {chapter_num}')
                safe_title = self._sanitize_filename(title)
                output_filename = f"{chapter_num:02d}_{safe_title}.mp3"
                output_path = output_dir / output_filename
                
                # Export processed chapter
                audio.export(
                    output_path,
                    format=self.default_format,
                    bitrate=self.default_bitrate,
                    parameters=["-ar", str(self.default_sample_rate)]
                )
                
                chapter_info = {
                    'title': title,
                    'chapter_number': chapter_num,
                    'audio_file': str(output_path),
                    'duration': len(audio) / 1000,  # Duration in seconds
                    'file_size': output_path.stat().st_size
                }
                
                processed_chapters.append(chapter_info)
                chapter_files.append(str(output_path))
                
            except Exception as e:
                self.logger.error(f"Error processing chapter: {e}")
                continue
        
        audiobook_info = {
            'chapters': processed_chapters,
            'total_chapters': len(processed_chapters),
            'total_duration': sum(ch['duration'] for ch in processed_chapters),
            'output_directory': str(output_dir)
        }
        
        # Create combined audiobook if requested
        if create_combined and chapter_files:
            combined_filename = "complete_audiobook.mp3"
            combined_path = output_dir / combined_filename
            
            self.combine_audio_files(
                audio_files=chapter_files,
                output_path=combined_path,
                add_pauses=True,
                normalize_audio=True,
                fade_in_out=True
            )
            
            audiobook_info['combined_file'] = str(combined_path)
            audiobook_info['combined_file_size'] = combined_path.stat().st_size
        
        # Create metadata file
        self._create_metadata_file(audiobook_info, output_dir)
        
        return audiobook_info
    
    def optimize_for_streaming(self, audio_file: str, output_file: str = None) -> str:
        """
        Optimize audio file for streaming/mobile playback
        
        Args:
            audio_file: Input audio file
            output_file: Output file (optional)
            
        Returns:
            Path to optimized file
        """
        if not output_file:
            path = Path(audio_file)
            output_file = path.parent / f"{path.stem}_optimized{path.suffix}"
        
        self.logger.info(f"Optimizing audio for streaming: {audio_file}")
        
        # Load audio
        audio = AudioSegment.from_file(audio_file)
        
        # Optimize settings for streaming
        # Lower bitrate and sample rate for smaller file size
        streaming_bitrate = "64k"
        streaming_sample_rate = 16000
        
        # Apply compression and normalization
        audio = normalize(audio)
        audio = compress_dynamic_range(audio, threshold=-20.0, ratio=4.0)
        
        # Export optimized version
        audio.export(
            output_file,
            format="mp3",
            bitrate=streaming_bitrate,
            parameters=[
                "-ar", str(streaming_sample_rate),
                "-ac", "1",  # Mono
                "-q:a", "2"  # Good quality
            ]
        )
        
        # Log file size comparison
        original_size = Path(audio_file).stat().st_size
        optimized_size = Path(output_file).stat().st_size
        compression_ratio = (1 - optimized_size / original_size) * 100
        
        self.logger.info(f"Optimization complete: {compression_ratio:.1f}% size reduction")
        
        return str(output_file)
    
    def add_intro_outro(self, audio_file: str, intro_text: str = None,
                       outro_text: str = None, voice_id: str = None) -> str:
        """
        Add intro and outro to audiobook
        
        Args:
            audio_file: Main audiobook file
            intro_text: Introduction text
            outro_text: Outro text
            voice_id: Voice ID for intro/outro
            
        Returns:
            Path to audiobook with intro/outro
        """
        # This would require TTS integration for intro/outro
        # For now, just return the original file
        self.logger.info("Intro/outro feature requires TTS integration")
        return audio_file
    
    def create_chapters_playlist(self, chapters: List[Dict], output_dir: str) -> str:
        """
        Create M3U playlist file for chapters
        
        Args:
            chapters: List of chapter dictionaries
            output_dir: Output directory
            
        Returns:
            Path to playlist file
        """
        playlist_path = Path(output_dir) / "audiobook_chapters.m3u"
        
        with open(playlist_path, 'w', encoding='utf-8') as f:
            f.write("#EXTM3U\n")
            f.write("#PLAYLIST:Audiobook Chapters\n\n")
            
            for chapter in chapters:
                duration = int(chapter.get('duration', 0))
                title = chapter.get('title', 'Unknown Chapter')
                filename = Path(chapter.get('audio_file', '')).name
                
                f.write(f"#EXTINF:{duration},{title}\n")
                f.write(f"{filename}\n\n")
        
        self.logger.info(f"Playlist created: {playlist_path}")
        return str(playlist_path)
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for cross-platform compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        filename = filename[:50].strip(' .')
        return filename or "untitled"
    
    def _create_metadata_file(self, audiobook_info: Dict, output_dir: Path):
        """Create metadata file for audiobook"""
        metadata_path = output_dir / "audiobook_metadata.txt"
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            f.write("AUDIOBOOK METADATA\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Total Chapters: {audiobook_info['total_chapters']}\n")
            f.write(f"Total Duration: {audiobook_info['total_duration']:.1f} seconds\n")
            f.write(f"Total Duration: {audiobook_info['total_duration']/3600:.1f} hours\n\n")
            
            f.write("CHAPTERS:\n")
            f.write("-" * 30 + "\n")
            for chapter in audiobook_info['chapters']:
                f.write(f"{chapter['chapter_number']:02d}. {chapter['title']}\n")
                f.write(f"    Duration: {chapter['duration']:.1f}s\n")
                f.write(f"    File: {Path(chapter['audio_file']).name}\n\n")
        
        self.logger.info(f"Metadata file created: {metadata_path}")

#!/usr/bin/env python3
"""
Test Script for PDF to Audiobook Converter
Author: inkbytefo
"""

import os
import sys
import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from pdf_extractor import PDFExtractor, TextCleaner
from elevenlabs_tts import ElevenLabsTTS
from audio_processor import AudioProcessor
from pdf_to_audiobook import PDFToAudiobookConverter

class TestPDFExtractor(unittest.TestCase):
    """Test PDF extraction functionality"""
    
    def setUp(self):
        self.extractor = PDFExtractor()
        self.text_cleaner = TextCleaner()
    
    def test_text_cleaner_basic(self):
        """Test basic text cleaning"""
        dirty_text = "This   is    a   test.\n\n\nWith   multiple   spaces."
        cleaned = self.text_cleaner.clean_text(dirty_text)
        
        self.assertNotIn("   ", cleaned)  # No triple spaces
        self.assertIn("This is a test", cleaned)
    
    def test_text_cleaner_abbreviations(self):
        """Test abbreviation expansion"""
        text_with_abbrev = "<PERSON><PERSON> and <PERSON><PERSON> met at 3 p.m."
        cleaned = self.text_cleaner.clean_text(text_with_abbrev)
        
        self.assertIn("<PERSON>", cleaned)
        self.assertIn("Professor <PERSON>", cleaned)
    
    def test_chunk_splitting(self):
        """Test text chunking"""
        long_text = "This is a sentence. " * 1000  # Very long text
        chunks = self.text_cleaner.split_into_chunks(long_text, max_length=1000)
        
        self.assertGreater(len(chunks), 1)
        for chunk in chunks:
            self.assertLessEqual(len(chunk), 1000)
    
    def test_chapter_detection(self):
        """Test chapter detection"""
        text_with_chapters = """
        Chapter 1: Introduction
        This is the first chapter content.
        
        Chapter 2: Main Content
        This is the second chapter content.
        
        Chapter 3: Conclusion
        This is the final chapter.
        """
        
        chapters = self.extractor._detect_chapters(text_with_chapters)
        self.assertGreaterEqual(len(chapters), 3)

class TestElevenLabsTTS(unittest.TestCase):
    """Test ElevenLabs TTS functionality"""
    
    def setUp(self):
        # Mock the ElevenLabs client
        with patch('elevenlabs_tts.ElevenLabs') as mock_client:
            self.tts = ElevenLabsTTS(api_key="test_key", voice_id="test_voice")
            self.tts.client = mock_client
    
    def test_initialization(self):
        """Test TTS initialization"""
        self.assertEqual(self.tts.api_key, "test_key")
        self.assertEqual(self.tts.voice_id, "test_voice")
    
    def test_filename_sanitization(self):
        """Test filename sanitization"""
        dirty_filename = "Chapter: 1 <Test> File/Name?"
        clean_filename = self.tts._sanitize_filename(dirty_filename)
        
        self.assertNotIn(":", clean_filename)
        self.assertNotIn("<", clean_filename)
        self.assertNotIn(">", clean_filename)
        self.assertNotIn("/", clean_filename)
        self.assertNotIn("?", clean_filename)
    
    @patch('time.sleep')
    def test_rate_limiting(self, mock_sleep):
        """Test rate limiting functionality"""
        self.tts._wait_for_rate_limit()
        self.tts._wait_for_rate_limit()
        
        # Should call sleep at least once for rate limiting
        mock_sleep.assert_called()

class TestAudioProcessor(unittest.TestCase):
    """Test audio processing functionality"""
    
    def setUp(self):
        self.processor = AudioProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_filename_sanitization(self):
        """Test filename sanitization"""
        dirty_filename = "Chapter: 1 <Test> File/Name?"
        clean_filename = self.processor._sanitize_filename(dirty_filename)
        
        self.assertNotIn(":", clean_filename)
        self.assertNotIn("<", clean_filename)
        self.assertNotIn(">", clean_filename)
    
    def test_metadata_creation(self):
        """Test metadata file creation"""
        audiobook_info = {
            'total_chapters': 3,
            'total_duration': 3600,
            'chapters': [
                {'chapter_number': 1, 'title': 'Chapter 1', 'duration': 1200, 'audio_file': 'ch1.mp3'},
                {'chapter_number': 2, 'title': 'Chapter 2', 'duration': 1200, 'audio_file': 'ch2.mp3'},
                {'chapter_number': 3, 'title': 'Chapter 3', 'duration': 1200, 'audio_file': 'ch3.mp3'},
            ]
        }
        
        self.processor._create_metadata_file(audiobook_info, Path(self.temp_dir))
        
        metadata_file = Path(self.temp_dir) / "audiobook_metadata.txt"
        self.assertTrue(metadata_file.exists())
        
        content = metadata_file.read_text()
        self.assertIn("Total Chapters: 3", content)
        self.assertIn("Chapter 1", content)

class TestPDFToAudiobookConverter(unittest.TestCase):
    """Test main converter functionality"""
    
    def setUp(self):
        self.converter = PDFToAudiobookConverter()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_config_loading(self):
        """Test configuration loading"""
        config = self.converter.load_default_config()
        
        self.assertIn('elevenlabs', config)
        self.assertIn('audio', config)
        self.assertIn('text', config)
        self.assertIn('output', config)
    
    def test_config_validation(self):
        """Test configuration validation"""
        # Test with missing API key
        with self.assertRaises(ValueError):
            converter = PDFToAudiobookConverter()
            converter.config['elevenlabs']['api_key'] = None
            converter.initialize_tts()

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    @patch('elevenlabs_tts.ElevenLabs')
    def test_mock_conversion_workflow(self, mock_elevenlabs):
        """Test the complete conversion workflow with mocked services"""
        
        # Mock ElevenLabs client
        mock_client = MagicMock()
        mock_elevenlabs.return_value = mock_client
        
        # Mock audio generation
        mock_client.generate.return_value = [b"fake_audio_data"]
        
        # Create a simple test text file (simulating PDF extraction)
        test_text = "Chapter 1: Test Chapter\nThis is test content for the audiobook."
        
        # Mock the PDF extractor
        with patch('pdf_to_audiobook.PDFExtractor') as mock_extractor:
            mock_extractor.return_value.extract_text.return_value = {
                'text': test_text,
                'metadata': {'title': 'Test Book', 'author': 'Test Author', 'pages': 1},
                'chapters': [
                    {'title': 'Chapter 1: Test Chapter', 'content': 'This is test content for the audiobook.'}
                ]
            }
            
            # Create converter with test config
            converter = PDFToAudiobookConverter()
            converter.config['elevenlabs']['api_key'] = 'test_key'
            converter.config['elevenlabs']['voice_id'] = 'test_voice'
            
            # This would normally fail without a real PDF, but we're testing the workflow
            # In a real test, you'd need a sample PDF file

def run_performance_test():
    """Run performance tests with sample data"""
    print("🚀 Running Performance Tests...")
    
    # Test text processing speed
    import time
    
    text_cleaner = TextCleaner()
    large_text = "This is a test sentence. " * 10000
    
    start_time = time.time()
    cleaned_text = text_cleaner.clean_text(large_text)
    chunks = text_cleaner.split_into_chunks(cleaned_text, max_length=5000)
    end_time = time.time()
    
    print(f"✅ Text processing: {end_time - start_time:.2f}s for {len(large_text)} characters")
    print(f"✅ Generated {len(chunks)} chunks")
    
    # Test chapter detection
    chapter_text = """
    Chapter 1: Introduction
    This is the introduction content.
    
    Chapter 2: Main Content  
    This is the main content.
    
    Chapter 3: Conclusion
    This is the conclusion.
    """
    
    extractor = PDFExtractor()
    start_time = time.time()
    chapters = extractor._detect_chapters(chapter_text)
    end_time = time.time()
    
    print(f"✅ Chapter detection: {end_time - start_time:.4f}s for {len(chapters)} chapters")

def run_api_connectivity_test():
    """Test API connectivity (requires real API key)"""
    print("🔗 Testing API Connectivity...")
    
    api_key = os.getenv('ELEVENLABS_API_KEY')
    if not api_key:
        print("⚠️  No API key found. Set ELEVENLABS_API_KEY to test connectivity.")
        return
    
    try:
        tts = ElevenLabsTTS(api_key=api_key)
        voices = tts.get_available_voices()
        print(f"✅ API connectivity successful. Found {len(voices)} voices.")
        
        # Test usage info
        usage_info = tts.get_usage_info()
        print(f"✅ Character limit: {usage_info['character_limit']}")
        print(f"✅ Characters used: {usage_info['character_count']}")
        
    except Exception as e:
        print(f"❌ API connectivity failed: {e}")

def main():
    """Run all tests"""
    print("🧪 PDF to Audiobook Converter - Test Suite")
    print("=" * 50)
    
    # Run unit tests
    print("\n📋 Running Unit Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    print("\n⚡ Running Performance Tests...")
    run_performance_test()
    
    # Run API connectivity test
    print("\n🌐 Running API Connectivity Test...")
    run_api_connectivity_test()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
PDF to Audiobook Converter - GUI Interface
Author: inkbytefo
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import logging
from pathlib import Path
import json
from pdf_to_audiobook import PDFToAudiobookConverter

class AudiobookConverterGUI:
    """Simple GUI interface for PDF to Audiobook conversion"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("PDF to Audiobook Converter")
        self.root.geometry("800x600")
        
        # Initialize converter
        self.converter = PDFToAudiobookConverter()
        
        # Queue for thread communication
        self.queue = queue.Queue()
        
        # Variables
        self.pdf_path = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.voice_id = tk.StringVar()
        self.extraction_method = tk.StringVar(value="auto")
        
        # Conversion state
        self.is_converting = False
        
        self.setup_ui()
        self.load_voices()
        
        # Start queue processing
        self.process_queue()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="PDF to Audiobook Converter", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # PDF File Selection
        ttk.Label(main_frame, text="PDF File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.pdf_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", command=self.browse_pdf).grid(row=1, column=2, pady=5)
        
        # Output Directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_dir, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", command=self.browse_output).grid(row=2, column=2, pady=5)
        
        # Voice Selection
        ttk.Label(main_frame, text="Voice:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.voice_combo = ttk.Combobox(main_frame, textvariable=self.voice_id, width=47)
        self.voice_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Refresh", command=self.load_voices).grid(row=3, column=2, pady=5)
        
        # Extraction Method
        ttk.Label(main_frame, text="Extraction Method:").grid(row=4, column=0, sticky=tk.W, pady=5)
        method_combo = ttk.Combobox(main_frame, textvariable=self.extraction_method, 
                                   values=["auto", "pdfplumber", "pypdf2", "pymupdf"],
                                   state="readonly", width=47)
        method_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        
        # Options Frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
        options_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(0, weight=1)
        
        # Checkboxes for options
        self.create_combined = tk.BooleanVar(value=True)
        self.create_chapters = tk.BooleanVar(value=True)
        self.optimize_streaming = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(options_frame, text="Create combined audiobook file", 
                       variable=self.create_combined).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Create separate chapter files", 
                       variable=self.create_chapters).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Optimize for streaming", 
                       variable=self.optimize_streaming).grid(row=2, column=0, sticky=tk.W)
        
        # Convert Button
        self.convert_button = ttk.Button(main_frame, text="Convert to Audiobook", 
                                        command=self.start_conversion)
        self.convert_button.grid(row=6, column=0, columnspan=3, pady=20)
        
        # Progress Bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # Status Label
        self.status_label = ttk.Label(main_frame, text="Ready to convert")
        self.status_label.grid(row=8, column=0, columnspan=3, pady=5)
        
        # Log Text Area
        log_frame = ttk.LabelFrame(main_frame, text="Conversion Log", padding="5")
        log_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(9, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Setup logging to GUI
        self.setup_gui_logging()
    
    def setup_gui_logging(self):
        """Setup logging to display in GUI"""
        class GUILogHandler(logging.Handler):
            def __init__(self, queue):
                super().__init__()
                self.queue = queue
            
            def emit(self, record):
                self.queue.put(('log', self.format(record)))
        
        # Add GUI handler to root logger
        gui_handler = GUILogHandler(self.queue)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(gui_handler)
        logging.getLogger().setLevel(logging.INFO)
    
    def browse_pdf(self):
        """Browse for PDF file"""
        filename = filedialog.askopenfilename(
            title="Select PDF file",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.pdf_path.set(filename)
            
            # Auto-set output directory
            if not self.output_dir.get():
                pdf_path = Path(filename)
                output_path = pdf_path.parent / "audiobooks" / pdf_path.stem
                self.output_dir.set(str(output_path))
    
    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select output directory")
        if directory:
            self.output_dir.set(directory)
    
    def load_voices(self):
        """Load available voices"""
        try:
            self.status_label.config(text="Loading voices...")
            voices = self.converter.list_available_voices()
            
            voice_options = []
            for voice in voices:
                voice_options.append(f"{voice['voice_id']} - {voice['name']} ({voice['category']})")
            
            self.voice_combo['values'] = voice_options
            if voice_options:
                self.voice_combo.current(0)
            
            self.status_label.config(text=f"Loaded {len(voices)} voices")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load voices: {e}")
            self.status_label.config(text="Error loading voices")
    
    def start_conversion(self):
        """Start the conversion process in a separate thread"""
        if self.is_converting:
            return
        
        # Validate inputs
        if not self.pdf_path.get():
            messagebox.showerror("Error", "Please select a PDF file")
            return
        
        if not self.voice_id.get():
            messagebox.showerror("Error", "Please select a voice")
            return
        
        # Extract voice ID from combo selection
        voice_selection = self.voice_id.get()
        actual_voice_id = voice_selection.split(' - ')[0] if ' - ' in voice_selection else voice_selection
        
        # Update converter config
        self.converter.config['output']['create_combined'] = self.create_combined.get()
        self.converter.config['output']['create_chapters'] = self.create_chapters.get()
        self.converter.config['output']['optimize_streaming'] = self.optimize_streaming.get()
        
        # Start conversion in thread
        self.is_converting = True
        self.convert_button.config(state='disabled', text='Converting...')
        self.progress.start()
        
        conversion_thread = threading.Thread(
            target=self.run_conversion,
            args=(self.pdf_path.get(), self.output_dir.get(), actual_voice_id, self.extraction_method.get())
        )
        conversion_thread.daemon = True
        conversion_thread.start()
    
    def run_conversion(self, pdf_path, output_dir, voice_id, extraction_method):
        """Run the conversion process"""
        try:
            result = self.converter.convert_pdf_to_audiobook(
                pdf_path=pdf_path,
                output_dir=output_dir,
                voice_id=voice_id,
                extraction_method=extraction_method
            )
            
            self.queue.put(('success', result))
            
        except Exception as e:
            self.queue.put(('error', str(e)))
    
    def process_queue(self):
        """Process messages from the conversion thread"""
        try:
            while True:
                message_type, data = self.queue.get_nowait()
                
                if message_type == 'log':
                    self.log_text.insert(tk.END, data + '\n')
                    self.log_text.see(tk.END)
                
                elif message_type == 'success':
                    self.conversion_complete(data)
                
                elif message_type == 'error':
                    self.conversion_error(data)
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_queue)
    
    def conversion_complete(self, result):
        """Handle successful conversion"""
        self.is_converting = False
        self.convert_button.config(state='normal', text='Convert to Audiobook')
        self.progress.stop()
        
        self.status_label.config(text="Conversion completed successfully!")
        
        # Show success message
        message = f"Audiobook created successfully!\n\n"
        message += f"Chapters: {result['total_chapters']}\n"
        message += f"Duration: {result['total_duration']/3600:.1f} hours\n"
        message += f"Output: {result['output_directory']}"
        
        messagebox.showinfo("Success", message)
    
    def conversion_error(self, error_message):
        """Handle conversion error"""
        self.is_converting = False
        self.convert_button.config(state='normal', text='Convert to Audiobook')
        self.progress.stop()
        
        self.status_label.config(text="Conversion failed")
        messagebox.showerror("Error", f"Conversion failed:\n{error_message}")


def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    app = AudiobookConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

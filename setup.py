#!/usr/bin/env python3
"""
PDF to Audiobook Converter Setup Script
Author: inkbytefo
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required Python packages"""
    print("📦 Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    return True

def setup_environment():
    """Setup environment configuration"""
    print("🔧 Setting up environment...")
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        print("📝 Creating .env file from template...")
        with open('.env.example', 'r') as template:
            content = template.read()
        with open('.env', 'w') as env_file:
            env_file.write(content)
        print("⚠️  Please edit .env file with your ElevenLabs API key!")
    
    # Create output directories
    directories = ['output', 'temp', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    print("✅ Environment setup complete!")

def main():
    """Main setup function"""
    print("🚀 PDF to Audiobook Converter Setup")
    print("=" * 40)
    
    if install_requirements():
        setup_environment()
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file with your ElevenLabs API key")
        print("2. Run: python pdf_to_audiobook.py --help")
    else:
        print("\n❌ Setup failed. Please check the errors above.")

if __name__ == "__main__":
    main()

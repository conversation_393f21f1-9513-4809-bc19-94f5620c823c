#!/usr/bin/env python3
"""
PDF to Audiobook Converter - Main Application
Author: inkbytefo
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import json
from dotenv import load_dotenv
import click
from tqdm import tqdm

# Import our modules
from pdf_extractor import PDFExtractor, TextCleaner
from elevenlabs_tts import ElevenLabsTTS
from audio_processor import AudioProcessor

class PDFToAudiobookConverter:
    """Main converter class that orchestrates the entire process"""
    
    def __init__(self, config: Dict = None):
        """Initialize converter with configuration"""
        # Load environment variables
        load_dotenv()
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.pdf_extractor = PDFExtractor()
        self.text_cleaner = TextCleaner()
        self.audio_processor = AudioProcessor()
        
        # Configuration
        self.config = config or self.load_default_config()
        
        # Initialize TTS (will be done when needed)
        self.tts = None
        
    def setup_logging(self, level=logging.INFO):
        """Setup logging configuration"""
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/pdf_to_audiobook.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Create logs directory
        Path('logs').mkdir(exist_ok=True)
    
    def load_default_config(self) -> Dict:
        """Load default configuration"""
        return {
            'elevenlabs': {
                'api_key': os.getenv('ELEVENLABS_API_KEY'),
                'voice_id': os.getenv('ELEVENLABS_VOICE_ID'),
                'model': 'eleven_multilingual_v2'
            },
            'audio': {
                'format': 'mp3',
                'bitrate': '128k',
                'sample_rate': 22050,
                'normalize': True,
                'add_pauses': True
            },
            'text': {
                'max_chunk_length': 5000,
                'clean_text': True,
                'detect_chapters': True
            },
            'output': {
                'create_combined': True,
                'create_chapters': True,
                'create_playlist': True,
                'optimize_streaming': False
            }
        }
    
    def initialize_tts(self):
        """Initialize TTS client"""
        if self.tts is None:
            api_key = self.config['elevenlabs']['api_key']
            voice_id = self.config['elevenlabs']['voice_id']
            
            if not api_key:
                raise ValueError("ElevenLabs API key not found. Please set ELEVENLABS_API_KEY environment variable.")
            
            self.tts = ElevenLabsTTS(api_key=api_key, voice_id=voice_id)
            self.logger.info("ElevenLabs TTS initialized")
    
    def convert_pdf_to_audiobook(self, pdf_path: str, output_dir: str = None,
                               voice_id: str = None, extraction_method: str = "auto") -> Dict:
        """
        Convert PDF to audiobook
        
        Args:
            pdf_path: Path to PDF file
            output_dir: Output directory (optional)
            voice_id: Voice ID to use (optional)
            extraction_method: PDF extraction method
            
        Returns:
            Dictionary with conversion results
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Setup output directory
        if not output_dir:
            output_dir = Path("output") / pdf_path.stem
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Starting PDF to audiobook conversion: {pdf_path.name}")
        self.logger.info(f"Output directory: {output_dir}")
        
        try:
            # Step 1: Extract text from PDF
            self.logger.info("Step 1: Extracting text from PDF...")
            extraction_result = self.pdf_extractor.extract_text(
                pdf_path=str(pdf_path),
                method=extraction_method
            )
            
            # Step 2: Clean and process text
            self.logger.info("Step 2: Cleaning and processing text...")
            if self.config['text']['clean_text']:
                cleaned_text = self.text_cleaner.clean_text(extraction_result['text'])
                extraction_result['text'] = cleaned_text
                
                # Clean chapter content too
                for chapter in extraction_result['chapters']:
                    chapter['content'] = self.text_cleaner.clean_text(chapter['content'])
            
            # Step 3: Initialize TTS
            self.logger.info("Step 3: Initializing text-to-speech...")
            self.initialize_tts()
            
            # Override voice if specified
            if voice_id:
                self.tts.set_voice(voice_id)
            
            # Step 4: Convert to audio
            self.logger.info("Step 4: Converting text to speech...")
            
            if self.config['text']['detect_chapters'] and len(extraction_result['chapters']) > 1:
                # Convert chapters separately
                converted_chapters = self.tts.convert_chapters(
                    chapters=extraction_result['chapters'],
                    output_dir=output_dir / "chapters",
                    model=self.config['elevenlabs']['model']
                )
            else:
                # Convert as single text with chunks
                text_chunks = self.text_cleaner.split_into_chunks(
                    text=extraction_result['text'],
                    max_length=self.config['text']['max_chunk_length']
                )
                
                audio_files = self.tts.convert_chunks(
                    text_chunks=text_chunks,
                    output_dir=output_dir / "chunks",
                    base_filename="audiobook_chunk",
                    model=self.config['elevenlabs']['model']
                )
                
                # Create chapter-like structure for processing
                converted_chapters = [{
                    'title': 'Complete Audiobook',
                    'chapter_number': 1,
                    'audio_file': audio_files[0] if audio_files else None,
                    'content': extraction_result['text']
                }]
            
            # Step 5: Process and combine audio
            self.logger.info("Step 5: Processing and combining audio...")
            audiobook_info = self.audio_processor.create_chapter_audiobook(
                chapters=converted_chapters,
                output_dir=output_dir,
                create_combined=self.config['output']['create_combined']
            )
            
            # Step 6: Create additional outputs
            if self.config['output']['create_playlist']:
                playlist_path = self.audio_processor.create_chapters_playlist(
                    chapters=audiobook_info['chapters'],
                    output_dir=output_dir
                )
                audiobook_info['playlist_file'] = playlist_path
            
            # Step 7: Optimize for streaming if requested
            if self.config['output']['optimize_streaming'] and 'combined_file' in audiobook_info:
                optimized_path = self.audio_processor.optimize_for_streaming(
                    audio_file=audiobook_info['combined_file'],
                    output_file=str(output_dir / "audiobook_streaming.mp3")
                )
                audiobook_info['streaming_file'] = optimized_path
            
            # Add conversion metadata
            audiobook_info.update({
                'source_pdf': str(pdf_path),
                'extraction_method': extraction_result.get('extraction_method', extraction_method),
                'pdf_metadata': extraction_result['metadata'],
                'voice_id': self.tts.voice_id,
                'conversion_config': self.config
            })
            
            # Save conversion report
            self.save_conversion_report(audiobook_info, output_dir)
            
            self.logger.info("✅ PDF to audiobook conversion completed successfully!")
            return audiobook_info
            
        except Exception as e:
            self.logger.error(f"❌ Conversion failed: {e}")
            raise
    
    def save_conversion_report(self, audiobook_info: Dict, output_dir: Path):
        """Save detailed conversion report"""
        report_path = output_dir / "conversion_report.json"
        
        # Create a clean report (remove non-serializable items)
        report = {
            'source_pdf': audiobook_info['source_pdf'],
            'total_chapters': audiobook_info['total_chapters'],
            'total_duration': audiobook_info['total_duration'],
            'extraction_method': audiobook_info['extraction_method'],
            'voice_id': audiobook_info['voice_id'],
            'output_files': {
                'combined_file': audiobook_info.get('combined_file'),
                'streaming_file': audiobook_info.get('streaming_file'),
                'playlist_file': audiobook_info.get('playlist_file')
            },
            'chapters': [
                {
                    'title': ch['title'],
                    'duration': ch['duration'],
                    'file_size': ch['file_size']
                }
                for ch in audiobook_info['chapters']
            ]
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Conversion report saved: {report_path}")
    
    def list_available_voices(self) -> List[Dict]:
        """List available ElevenLabs voices"""
        self.initialize_tts()
        return self.tts.get_available_voices()
    
    def get_usage_info(self) -> Dict:
        """Get ElevenLabs usage information"""
        self.initialize_tts()
        return {
            'character_limit': self.tts.get_character_limit(),
            'character_count': self.tts.get_character_count()
        }


# CLI Interface
@click.command()
@click.argument('pdf_path', type=click.Path(exists=True))
@click.option('--output-dir', '-o', help='Output directory')
@click.option('--voice-id', '-v', help='ElevenLabs voice ID')
@click.option('--extraction-method', '-e',
              type=click.Choice(['auto', 'pdfplumber', 'pypdf2', 'pymupdf']),
              default='auto', help='PDF extraction method')
@click.option('--list-voices', is_flag=True, help='List available voices')
@click.option('--usage-info', is_flag=True, help='Show usage information')
@click.option('--config-file', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose logging')
def main(pdf_path, output_dir, voice_id, extraction_method, list_voices,
         usage_info, config_file, verbose):
    """
    Convert PDF files to audiobooks using ElevenLabs TTS

    Example:
        python pdf_to_audiobook.py book.pdf -o ./audiobooks -v your_voice_id
    """

    # Setup logging level
    log_level = logging.DEBUG if verbose else logging.INFO

    try:
        # Initialize converter
        converter = PDFToAudiobookConverter()
        converter.setup_logging(log_level)

        # Handle special commands
        if list_voices:
            click.echo("Available ElevenLabs voices:")
            voices = converter.list_available_voices()
            for voice in voices:
                click.echo(f"  {voice['voice_id']}: {voice['name']} ({voice['category']})")
            return

        if usage_info:
            info = converter.get_usage_info()
            click.echo(f"Character limit: {info['character_limit']}")
            click.echo(f"Characters used: {info['character_count']}")
            remaining = info['character_limit'] - info['character_count']
            click.echo(f"Characters remaining: {remaining}")
            return

        # Convert PDF to audiobook
        click.echo(f"🚀 Starting conversion: {pdf_path}")

        result = converter.convert_pdf_to_audiobook(
            pdf_path=pdf_path,
            output_dir=output_dir,
            voice_id=voice_id,
            extraction_method=extraction_method
        )

        # Display results
        click.echo("\n✅ Conversion completed successfully!")
        click.echo(f"📁 Output directory: {result['output_directory']}")
        click.echo(f"📚 Total chapters: {result['total_chapters']}")
        click.echo(f"⏱️  Total duration: {result['total_duration']/3600:.1f} hours")

        if 'combined_file' in result:
            click.echo(f"🎵 Combined audiobook: {Path(result['combined_file']).name}")

        if 'playlist_file' in result:
            click.echo(f"📋 Playlist file: {Path(result['playlist_file']).name}")

    except Exception as e:
        click.echo(f"❌ Error: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
